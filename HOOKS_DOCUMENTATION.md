# Trading Agent Hooks System Documentation

This document describes the comprehensive hooks system implemented for the Solana Memecoin Trading Agent. The hooks provide automated delegation, workflow coordination, safety checks, and monitoring capabilities.

## Overview

The hooks system consists of 10 specialized hooks that work together to:
- **Automatically delegate** user prompts to appropriate sub-agents
- **Coordinate workflows** between multiple agents
- **Provide safety checks** for trade execution and file access
- **Monitor and log** all system activities
- **Validate data sources** and enforce rate limits

## Hook Configuration

The hooks are configured in `.claude/hooks.json` and are automatically loaded when Claude Code starts. The configuration includes:

### Hook Events and Triggers

| Event | Hook | Purpose |
|-------|------|---------|
| `UserPromptSubmit` | `agent_delegator.py` | Auto-delegate prompts to sub-agents |
| `PreToolUse` | `market_data_validator.py` | Validate market data sources |
| `PreToolUse` | `trade_execution_guard.py` | Safety checks for trade execution |
| `PreToolUse` | `portfolio_file_protector.py` | Protect sensitive files |
| `PostToolUse` | `workflow_coordinator.py` | Coordinate multi-agent workflows |
| `PostToolUse` | `market_data_logger.py` | Log market data requests |
| `SubagentStop` | `subagent_result_processor.py` | Process sub-agent results |
| `SessionStart` | `trading_session_init.py` | Initialize trading session |
| `SessionEnd` | `trading_session_cleanup.py` | Clean up trading session |
| `Stop` | `session_logger.py` | Log session activities |
| `Notification` | `trading_notifications.py` | Handle trading notifications |

## Individual Hook Details

### 1. Agent Delegator (`agent_delegator.py`)

**Purpose**: Automatically routes user prompts to appropriate specialized sub-agents

**Features**:
- Pattern matching for agent expertise areas
- Confidence scoring for delegation decisions
- Multi-agent workflow detection
- Comprehensive logging of delegation decisions

**Agent Patterns**:
- **market-analyzer**: market, analyze, opportunities, trends, volume, DEX
- **risk-manager**: risk, position size, stop loss, portfolio, exposure
- **trade-executor**: execute, trade, buy, sell, swap, order
- **portfolio-manager**: portfolio, performance, PnL, returns, tracking
- **token-researcher**: research, investigate, due diligence, scam detection
- **technical-analyst**: technical, chart, pattern, indicator, signal

**Example Output**:
```
✓ Auto-delegating to market-analyzer agent (confidence: 0.8)
  Suggested prompt: Use the market-analyzer agent to: analyze current memecoin opportunities
```

### 2. Workflow Coordinator (`workflow_coordinator.py`)

**Purpose**: Manages multi-agent workflows and coordinates agent handoffs

**Predefined Workflows**:
- **token_evaluation**: Research → Market Analysis → Technical Analysis → Risk Assessment
- **market_opportunity**: Market Scan → Technical Confirmation → Due Diligence → Risk Assessment
- **trade_execution**: Risk Check → Execute Trade → Portfolio Update
- **portfolio_review**: Performance Report → Risk Assessment → Market Analysis → Exit Signals
- **emergency_response**: Crisis Assessment → Emergency Protocols → Loss Calculation → Emergency Trades

**Example Output**:
```
✓ Starting workflow: token_evaluation
  Description: Complete token evaluation workflow
  First step: token-researcher - Research token fundamentals and detect risks
```

### 3. Trade Execution Guard (`trade_execution_guard.py`)

**Purpose**: Provides safety checks and validation before executing trades

**Safety Features**:
- Dangerous command pattern detection
- Trade amount validation and limits
- Daily volume tracking
- Wallet security checks
- Private key exposure prevention

**Trade Limits**:
- Single trade: $10,000 maximum
- Daily total: $50,000 maximum
- Portfolio percentage: 10% maximum per trade

**Example Output**:
```
⚠️  Trade Execution Safety Check
   Command Type: trading
   Risk Level: MEDIUM
   Trade Amount: $5,000.00
   Daily Volume: $15,000.00
   ✓ Command approved for execution
```

### 4. Market Data Validator (`market_data_validator.py`)

**Purpose**: Validates market data requests and ensures data quality

**Validation Features**:
- Trusted source verification
- Rate limiting enforcement
- Token address validation
- Suspicious pattern detection
- Request logging and monitoring

**Trusted Sources**:
- dexscreener.com
- birdeye.so
- coingecko.com
- solscan.io
- jupiter.ag
- raydium.io
- orca.so

**Rate Limits**:
- 60 requests per minute
- 1,000 requests per hour
- 5 maximum concurrent requests

### 5. Subagent Result Processor (`subagent_result_processor.py`)

**Purpose**: Processes and coordinates results from specialized sub-agents

**Processing Features**:
- Agent result identification
- Structured data extraction
- Next action determination
- Workflow completion checking
- Coordination summary generation

**Data Extraction Patterns**:
- **market-analyzer**: opportunities, trends, volume, risk levels
- **risk-manager**: position size, stop loss, risk scores
- **trade-executor**: transaction hash, execution price, slippage
- **portfolio-manager**: total value, PnL, returns, positions
- **token-researcher**: risk rating, recommendations, red flags
- **technical-analyst**: signals, entry prices, targets, confidence

### 6. Portfolio File Protector (`portfolio_file_protector.py`)

**Purpose**: Protects critical portfolio and configuration files

**Protected Patterns**:
- `.env` files
- Private key files
- Wallet configuration files
- Seed phrases and mnemonics
- Portfolio state files
- Trading configuration files

### 7. Session Management Hooks

**Trading Session Init** (`trading_session_init.py`):
- Creates necessary directories
- Initializes session logging
- Verifies agent availability
- Sets up coordination infrastructure

**Trading Session Cleanup** (`trading_session_cleanup.py`):
- Generates session summaries
- Preserves important logs
- Cleans up temporary files
- Records session metrics

### 8. Logging and Monitoring Hooks

**Market Data Logger** (`market_data_logger.py`):
- Logs all market data requests and responses
- Tracks data source usage
- Monitors data quality
- Provides audit trail

**Session Logger** (`session_logger.py`):
- Records session activities
- Tracks agent interactions
- Monitors system performance
- Provides debugging information

**Trading Notifications** (`trading_notifications.py`):
- Handles trading-related alerts
- Filters notification types
- Logs notification history
- Provides user feedback

## Log Files and Data Storage

All hooks store their data in `~/.claude/logs/` with the following structure:

```
~/.claude/logs/
├── agent_delegations.jsonl          # Agent delegation decisions
├── workflow_coordination.jsonl      # Workflow coordination events
├── workflow_delegations.jsonl       # Multi-agent workflow triggers
├── trade_execution_guard.jsonl      # Trade safety checks
├── market_data_requests.jsonl       # Market data validation
├── subagent_results.jsonl          # Sub-agent result processing
├── session_activities.jsonl        # Session activity logs
├── notifications.jsonl             # System notifications
├── hook_errors.jsonl               # Hook error logs
└── session_log.json                # Current session information
```

Additional coordination data is stored in:
```
~/.claude/coordination/
├── latest_result.json              # Latest sub-agent result
└── workflow_state.json             # Current workflow state
```

## Usage Examples

### Automatic Agent Delegation

```bash
# User input: "Analyze the current Solana memecoin market"
# Hook output:
✓ Auto-delegating to market-analyzer agent (confidence: 0.85)
  Suggested prompt: Use the market-analyzer agent to: Analyze the current Solana memecoin market
```

### Multi-Agent Workflow

```bash
# User input: "Complete analysis of $BONK token"
# Hook output:
✓ Multi-agent workflow detected: token_evaluation
  Suggested agent sequence: token-researcher → technical-analyst → risk-manager
```

### Trade Safety Check

```bash
# User input: "Execute trade to buy $5000 worth of $BONK"
# Hook output:
⚠️  Trade Execution Safety Check
   Command Type: trading
   Risk Level: MEDIUM
   Trade Amount: $5,000.00
   Daily Volume: $12,000.00
   Recommendations: Confirm trade parameters, Check market conditions
   ✓ Command approved for execution
```

## Configuration and Customization

### Modifying Agent Patterns

Edit `agent_delegator.py` to add new patterns or modify existing ones:

```python
AGENT_PATTERNS = {
    'market-analyzer': [
        r'\b(market|analyze|scan|opportunities)\b',
        # Add new patterns here
    ]
}
```

### Adjusting Trade Limits

Modify `trade_execution_guard.py` to change safety limits:

```python
MAX_TRADE_AMOUNTS = {
    'single_trade': 10000,    # Adjust maximum per trade
    'daily_total': 50000,     # Adjust daily limit
    'portfolio_percentage': 0.1  # Adjust portfolio percentage
}
```

### Adding New Workflows

Add new workflows to `workflow_coordinator.py`:

```python
TRADING_WORKFLOWS = {
    'new_workflow': {
        'description': 'Description of new workflow',
        'steps': [
            {'agent': 'agent-name', 'task': 'Task description'}
        ],
        'triggers': ['trigger phrases']
    }
}
```

## Troubleshooting

### Common Issues

1. **Hooks not executing**: Check file permissions with `ls -la .claude/hooks/`
2. **Permission errors**: Run `chmod +x .claude/hooks/*.py`
3. **Missing dependencies**: Ensure Python 3 is available
4. **Log directory issues**: Hooks will create directories automatically

### Debugging

Check hook error logs:
```bash
tail -f ~/.claude/logs/hook_errors.jsonl
```

Monitor agent delegations:
```bash
tail -f ~/.claude/logs/agent_delegations.jsonl
```

View workflow coordination:
```bash
tail -f ~/.claude/logs/workflow_coordination.jsonl
```

## Security Considerations

- Hooks run with your user permissions
- Sensitive data is never logged in plain text
- Private keys and seed phrases are protected
- Rate limiting prevents API abuse
- All file access is validated

## Performance Impact

- Hooks add minimal latency (< 100ms per hook)
- Logging is asynchronous where possible
- Error handling prevents blocking main workflow
- Resource usage is minimal

The hooks system provides a robust foundation for safe, coordinated, and monitored trading operations while maintaining the flexibility to customize and extend functionality as needed.
