# Solana Memecoin Trading Agent System

A sophisticated multi-agent trading system designed specifically for Solana memecoin trading, featuring specialized AI agents that collaborate to provide comprehensive market analysis, risk management, and trade execution.

## System Overview

This system consists of a **Main Coordinator Agent** that orchestrates six specialized sub-agents, each with specific expertise areas:

### 🎯 Main Coordinator Agent
- **File**: `main-agent.md`
- **Role**: Central orchestrator that coordinates all sub-agents and maintains overall strategy coherence
- **Responsibilities**: Strategic coordination, risk oversight, performance monitoring, communication hub

### 🔍 Specialized Sub-Agents

#### 1. Market Analysis Agent (`market-analyzer`)
- **Expertise**: Real-time market analysis and trend identification
- **Focus**: Solana DEX activity, memecoin trends, opportunity discovery
- **Tools**: Web search, data fetching, market monitoring

#### 2. Risk Management Agent (`risk-manager`)
- **Expertise**: Portfolio risk assessment and position sizing
- **Focus**: Risk calculation, stop-loss management, portfolio protection
- **Tools**: Risk calculation, portfolio analysis, stress testing

#### 3. Trade Execution Agent (`trade-executor`)
- **Expertise**: Trade execution and order management
- **Focus**: DEX trading, slippage optimization, MEV protection
- **Tools**: DEX integration, transaction management, execution optimization

#### 4. Portfolio Management Agent (`portfolio-manager`)
- **Expertise**: Portfolio tracking and optimization
- **Focus**: Performance tracking, rebalancing, analytics
- **Tools**: Portfolio analysis, performance calculation, reporting

#### 5. Token Research Agent (`token-researcher`)
- **Expertise**: Token research and due diligence
- **Focus**: Project analysis, scam detection, fundamental research
- **Tools**: Web research, contract analysis, team verification

#### 6. Technical Analysis Agent (`technical-analyst`)
- **Expertise**: Chart analysis and technical indicators
- **Focus**: Price patterns, entry/exit signals, technical timing
- **Tools**: Chart analysis, indicator calculation, pattern recognition

## Getting Started

### 1. System Setup

The agents are already configured in the `.claude/agents/` directory. To activate them:

```bash
# Verify agents are properly installed
ls -la .claude/agents/

# You should see:
# market-analyzer.md
# risk-manager.md
# trade-executor.md
# portfolio-manager.md
# token-researcher.md
# technical-analyst.md
```

### 2. Using the Agent System

#### Automatic Agent Delegation
Claude Code will automatically delegate tasks to appropriate agents based on your requests:

```
# Market analysis will trigger the market-analyzer agent
"Analyze the current Solana memecoin market for opportunities"

# Risk assessment will trigger the risk-manager agent
"Calculate position size for a $BONK trade with $10,000 portfolio"

# Research requests will trigger the token-researcher agent
"Research the new token $EXAMPLE for potential investment"
```

#### Explicit Agent Invocation
You can explicitly request specific agents:

```
# Explicitly use the technical analyst
"Use the technical-analyst agent to analyze $SOL charts"

# Explicitly use the market analyzer
"Have the market-analyzer agent scan for new opportunities"

# Chain multiple agents
"Use token-researcher to analyze $TOKEN, then technical-analyst for entry timing"
```

### 3. Typical Workflow

#### Discovery Phase
1. **Market Scanning**: Market Analysis Agent identifies opportunities
2. **Initial Research**: Token Research Agent conducts due diligence
3. **Technical Analysis**: Technical Analysis Agent identifies entry points

#### Decision Phase
1. **Risk Assessment**: Risk Management Agent calculates position size
2. **Final Analysis**: Main Agent aggregates all insights
3. **Go/No-Go Decision**: Main Agent makes final trading decision

#### Execution Phase
1. **Trade Execution**: Trade Execution Agent executes the trade
2. **Portfolio Update**: Portfolio Management Agent tracks the position
3. **Ongoing Monitoring**: All agents monitor for exit signals

## Agent Capabilities

### Market Analysis Agent
- Real-time DEX monitoring (Jupiter, Raydium, Orca)
- Volume and liquidity analysis
- Trend identification and momentum analysis
- Social sentiment tracking
- Opportunity discovery and alert generation

### Risk Management Agent
- Position sizing using Kelly Criterion and volatility adjustment
- Portfolio risk monitoring and correlation analysis
- Stop-loss and take-profit management
- Stress testing and scenario analysis
- Emergency risk protocols

### Trade Execution Agent
- Multi-DEX routing optimization
- MEV protection and slippage management
- Transaction cost optimization
- Order management and retry logic
- Execution quality monitoring

### Portfolio Management Agent
- Real-time position tracking and P&L calculation
- Performance attribution and risk-adjusted returns
- Portfolio rebalancing and optimization
- Comprehensive reporting and analytics
- Benchmark comparison and analysis

### Token Research Agent
- Comprehensive project due diligence
- Smart contract analysis and vulnerability assessment
- Team verification and background checks
- Tokenomics analysis and red flag detection
- Ongoing monitoring of held positions

### Technical Analysis Agent
- Multi-timeframe chart analysis
- Technical indicator analysis (RSI, MACD, Bollinger Bands, etc.)
- Chart pattern recognition and breakout analysis
- Support/resistance level identification
- Entry/exit signal generation

## Configuration and Customization

### Risk Parameters
Default risk settings can be modified in the Main Agent configuration:
- Maximum position size: 5% of portfolio
- Stop-loss threshold: -15% per position
- Take-profit targets: +50%, +100%, +200%
- Maximum total exposure: 80% of portfolio

### Agent Customization
Each agent can be customized by editing their respective `.md` files:
- Modify system prompts for different behavior
- Adjust tool access permissions
- Change model selection (sonnet, opus, haiku)
- Update expertise areas and focus

### Adding New Agents
To add new specialized agents:
1. Create a new `.md` file in `.claude/agents/`
2. Follow the existing agent format with YAML frontmatter
3. Define the agent's expertise and responsibilities
4. Update the Main Agent to coordinate with the new agent

## Best Practices

### 1. Start with Small Positions
- Begin with small position sizes to test the system
- Gradually increase position sizes as confidence builds
- Always respect risk management guidelines

### 2. Monitor Agent Performance
- Track the accuracy of each agent's recommendations
- Adjust agent parameters based on performance
- Regularly review and update agent configurations

### 3. Use Multiple Confirmations
- Seek confirmation from multiple agents before major decisions
- Don't rely on a single agent for critical decisions
- Use the Main Agent to synthesize insights from all agents

### 4. Stay Updated
- Keep agents updated with latest market developments
- Regularly review and update risk parameters
- Adapt strategies based on changing market conditions

## Safety and Risk Management

### Built-in Safety Features
- Multiple risk assessment layers
- Automatic stop-loss triggers
- Position size limits and exposure controls
- Emergency liquidation protocols

### Manual Overrides
- All automated decisions can be manually overridden
- Emergency stop functionality for all trading
- Manual position management capabilities
- Human oversight and approval workflows

### Monitoring and Alerts
- Real-time portfolio monitoring
- Automated alert system for significant events
- Performance tracking and reporting
- Risk metric monitoring and alerts

## Support and Troubleshooting

### Common Issues
- **Agent not responding**: Check agent configuration and tool permissions
- **Execution failures**: Verify DEX connectivity and wallet setup
- **Performance issues**: Review risk parameters and position sizing
- **Data accuracy**: Verify data sources and API connections

### Getting Help
- Review agent logs for detailed error information
- Check the Main Agent coordination for workflow issues
- Verify all required tools and permissions are properly configured
- Consult individual agent documentation for specific issues

## Disclaimer

This trading system is for educational and research purposes. Cryptocurrency trading involves significant risk, and you should never trade with money you cannot afford to lose. Always conduct your own research and consider consulting with financial professionals before making investment decisions.

The agents provide analysis and recommendations but do not guarantee profitable trades. Past performance does not indicate future results. Use this system at your own risk and always maintain appropriate risk management practices.
