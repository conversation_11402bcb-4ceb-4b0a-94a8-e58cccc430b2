# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Solana Memecoin Trading Agent System** - a sophisticated multi-agent AI trading system designed specifically for Solana memecoin trading. The system uses a Main Coordinator Agent that orchestrates six specialized sub-agents to provide comprehensive market analysis, risk management, and trade execution.

## Architecture

### Agent System Structure

The codebase implements a **documentation-driven agent architecture** where each component is defined in markdown files:

- **Main Coordinator Agent** (`main-agent.md`): Central orchestrator managing strategy, risk oversight, performance monitoring, and communication between sub-agents
- **Specialized Sub-Agents** (`.claude/agents/`): Six expert agents each handling specific domains

#### Sub-Agent Ecosystem

| Agent | File | Expertise | Primary Function |
|-------|------|-----------|------------------|
| `market-analyzer` | `.claude/agents/market-analyzer.md` | Real-time market analysis | Monitors Solana DEXs, identifies trends and opportunities |
| `risk-manager` | `.claude/agents/risk-manager.md` | Portfolio risk assessment | Calculates position sizes, manages stop-losses |
| `trade-executor` | `.claude/agents/trade-executor.md` | Trade execution | Executes trades on DEXs with MEV protection |
| `portfolio-manager` | `.claude/agents/portfolio-manager.md` | Portfolio tracking | Tracks performance, handles rebalancing |
| `token-researcher` | `.claude/agents/token-researcher.md` | Token due diligence | Researches projects, detects scams |
| `technical-analyst` | `.claude/agents/technical-analyst.md` | Chart analysis | Provides technical analysis and entry/exit signals |

### Trading Workflow

1. **Discovery Phase**: Market scanning → Initial research → Technical analysis
2. **Decision Phase**: Risk assessment → Insight aggregation → Go/No-go decision
3. **Execution Phase**: Trade execution → Portfolio update → Ongoing monitoring

## System Configuration

### Risk Parameters (Main Agent)
- Maximum position size: 5% of portfolio
- Maximum total exposure: 80% of portfolio
- Stop-loss threshold: -15% per position
- Take-profit targets: +50%, +100%, +200%

### Market Parameters
- Minimum liquidity threshold: $100K
- Maximum slippage tolerance: 3%
- Preferred DEXs: Jupiter, Raydium, Orca
- Trading: 24/7 with reduced activity during low volume

### Performance Targets
- Target monthly return: 20%
- Maximum drawdown tolerance: 25%
- Minimum win rate: 60%
- Risk-adjusted return (Sharpe): >1.5

## Agent Management

### Using Agents
Agents are automatically delegated based on task context, or can be explicitly invoked:

```bash
# View available agents
/agents

# Explicit invocation examples
"Use the technical-analyst agent to analyze $SOL charts"
"Have the market-analyzer agent scan for new opportunities"
"Use token-researcher to analyze $TOKEN, then technical-analyst for entry timing"
```

### Agent Files Location
All sub-agents are stored in `.claude/agents/` as markdown files with YAML frontmatter following the Claude Code subagent specification.

## Development Notes

- **No traditional build system**: This is a documentation-driven system without package.json, requirements.txt, or build scripts
- **Agent coordination**: The Main Agent coordinates all sub-agents through defined workflows and decision frameworks
- **Risk-first approach**: Multiple risk assessment layers with emergency protocols built-in
- **Hook-driven automation**: System uses Claude Code hooks for automatic agent delegation, workflow coordination, and safety checks
- **Integration points**: Designed to connect with Solana RPC nodes, DEX APIs, price feeds, and social media APIs

## Hook System Integration

This project uses an advanced Claude Code hook system for automated operations:

### Automatic Agent Delegation
User prompts are automatically routed to appropriate sub-agents based on content analysis:
- **Trigger**: `UserPromptSubmit` event
- **Implementation**: `.claude/hooks/agent_delegator.py`
- **Function**: Analyzes prompt content using regex patterns and confidence scoring
- **Priority System**: High-priority keywords (e.g., "emergency", "scam", "breakout") trigger immediate delegation
- **Logging**: All delegations logged to `~/.claude/logs/agent_delegations.jsonl` for analysis

### Safety and Validation Hooks
Multiple pre-execution hooks ensure system safety:
- **Market Data Validation**: Validates data sources and enforces rate limits
- **Trade Execution Guard**: Safety checks before any trade execution
- **Portfolio File Protection**: Prevents unauthorized access to sensitive files

### Workflow Coordination
Post-execution hooks manage multi-agent workflows:
- **Workflow Coordinator**: Orchestrates complex multi-step operations
- **Result Processor**: Aggregates and processes sub-agent outputs
- **Session Management**: Handles trading session initialization and cleanup

### Configuration
Hook configuration is stored in `.claude/hooks.json` with 10+ specialized hooks covering the complete trading workflow.

## Emergency Protocols

### Market Crash Response
1. Halt new position entries
2. Activate emergency stop-losses
3. Coordinate damage assessment with Risk Management Agent
4. Implement capital preservation mode

### System Failure Response
1. Pause automated trading
2. Notify human operator
3. Secure open positions
4. Implement manual override procedures

## File Structure

```
├── README.md                    # System overview and setup instructions
├── main-agent.md               # Main coordinator agent specification
├── .claude/
│   ├── agents/                 # Specialized sub-agent definitions
│   │   ├── market-analyzer.md      # Market analysis specialist
│   │   ├── risk-manager.md         # Risk management specialist
│   │   ├── trade-executor.md       # Trade execution specialist
│   │   ├── portfolio-manager.md    # Portfolio management specialist
│   │   ├── token-researcher.md     # Token research specialist
│   │   └── technical-analyst.md    # Technical analysis specialist
│   ├── hooks/                  # Automated workflow hooks
│   │   ├── agent_delegator.py      # Auto-delegate prompts to sub-agents
│   │   ├── workflow_coordinator.py # Multi-agent workflow management
│   │   ├── trade_execution_guard.py # Trade safety checks
│   │   ├── market_data_validator.py # Data source validation
│   │   └── [6+ additional hooks]   # Session management, logging, etc.
│   └── hooks.json              # Hook system configuration
├── SubAgents.md               # Documentation on Claude Code subagents
├── OutputStyles.md            # Output formatting documentation
├── ModelContextProtocol.md    # MCP integration documentation
├── TroubleShooting.md         # System troubleshooting guide
├── PythonSDK.md               # Python SDK documentation
├── HeadlessMode.md            # Headless operation documentation
├── ClaudeHooks.md             # Hook system documentation
└── HOOKS_DOCUMENTATION.md     # Detailed hooks implementation guide
```

This system represents a sophisticated approach to algorithmic trading using AI agents, with comprehensive risk management and emergency protocols built into the architecture.