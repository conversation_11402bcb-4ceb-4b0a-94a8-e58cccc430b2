{"hooks": {"UserPromptSubmit": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/agent_delegator.py"}]}], "PreToolUse": [{"matcher": "web-search|web-fetch", "hooks": [{"type": "command", "command": ".claude/hooks/market_data_validator.py"}]}, {"matcher": "launch-process", "hooks": [{"type": "command", "command": ".claude/hooks/trade_execution_guard.py"}]}, {"matcher": "str-replace-editor|save-file", "hooks": [{"type": "command", "command": ".claude/hooks/portfolio_file_protector.py"}]}], "PostToolUse": [{"matcher": "codebase-retrieval", "hooks": [{"type": "command", "command": ".claude/hooks/workflow_coordinator.py"}]}, {"matcher": "web-search|web-fetch", "hooks": [{"type": "command", "command": ".claude/hooks/market_data_logger.py"}]}], "SubagentStop": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/subagent_result_processor.py"}]}], "Stop": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/session_logger.py"}]}], "SessionStart": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/trading_session_init.py"}]}], "SessionEnd": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/trading_session_cleanup.py"}]}], "Notification": [{"matcher": "*", "hooks": [{"type": "command", "command": ".claude/hooks/trading_notifications.py"}]}]}}