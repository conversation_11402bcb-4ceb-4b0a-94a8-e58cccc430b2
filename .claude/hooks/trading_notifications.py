#!/usr/bin/env python3
"""
Trading Notifications Hook for Solana Memecoin Trading System
Handles trading-related notifications and alerts
"""
import json
import sys
import os
from datetime import datetime

def main():
    try:
        input_data = json.load(sys.stdin)
        
        # Check if this is a trading-related notification
        message = input_data.get('message', '').lower()
        
        trading_keywords = ['trade', 'position', 'risk', 'portfolio', 'market', 'token']
        
        if any(keyword in message for keyword in trading_keywords):
            print("📱 Trading System Notification")
            print(f"   {input_data.get('message', 'No message')}")
        
        # Log notification
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'notification': input_data.get('message', ''),
            'type': 'trading' if any(keyword in message for keyword in trading_keywords) else 'general'
        }
        
        with open(f"{log_dir}/notifications.jsonl", 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
    
    except Exception as e:
        pass  # Silent notification handling

if __name__ == "__main__":
    main()
