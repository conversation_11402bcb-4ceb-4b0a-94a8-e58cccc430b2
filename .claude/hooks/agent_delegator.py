#!/usr/bin/env python3
"""
Agent Delegator Hook for Solana Memecoin Trading System
Automatically routes user prompts to appropriate specialized sub-agents
"""
import json
import sys
import re
import os
from datetime import datetime

# Agent delegation patterns
AGENT_PATTERNS = {
    'market-analyzer': [
        r'\b(market|analyze|scan|opportunities|trends|volume|dex|price|chart)\b',
        r'\b(solana|jupiter|raydium|orca|memecoin|token)\b.*\b(analysis|monitor|track)\b',
        r'\b(find|discover|identify).*\b(opportunities|trends|signals)\b',
        r'\b(market\s+conditions|trading\s+volume|liquidity)\b'
    ],
    'risk-manager': [
        r'\b(risk|position\s+size|stop\s+loss|portfolio|exposure)\b',
        r'\b(calculate|assess|manage).*\b(risk|position|size)\b',
        r'\b(maximum\s+loss|drawdown|volatility|correlation)\b',
        r'\b(risk\s+management|position\s+sizing|stop\s+loss)\b'
    ],
    'trade-executor': [
        r'\b(execute|trade|buy|sell|swap|order)\b',
        r'\b(jupiter|raydium|orca|dex).*\b(trade|swap|execute)\b',
        r'\b(slippage|mev|transaction|gas|fee)\b',
        r'\b(place\s+order|execute\s+trade|make\s+trade)\b'
    ],
    'portfolio-manager': [
        r'\b(portfolio|performance|pnl|returns|tracking)\b',
        r'\b(rebalance|allocation|holdings|positions)\b',
        r'\b(sharpe|sortino|drawdown|benchmark)\b',
        r'\b(portfolio\s+analysis|performance\s+tracking)\b'
    ],
    'token-researcher': [
        r'\b(research|investigate|analyze|due\s+diligence)\b.*\b(token|project|team)\b',
        r'\b(scam|rug\s+pull|fraud|security|audit)\b',
        r'\b(tokenomics|whitepaper|roadmap|team)\b',
        r'\b(verify|check|validate).*\b(project|token|team)\b'
    ],
    'technical-analyst': [
        r'\b(technical|chart|pattern|indicator|signal)\b',
        r'\b(rsi|macd|bollinger|support|resistance)\b',
        r'\b(entry|exit|breakout|trend|momentum)\b',
        r'\b(technical\s+analysis|chart\s+analysis|price\s+action)\b'
    ]
}

# High-priority keywords that should trigger immediate delegation
HIGH_PRIORITY_KEYWORDS = {
    'market-analyzer': ['urgent', 'immediate', 'breaking', 'alert', 'crash', 'pump'],
    'risk-manager': ['emergency', 'stop', 'loss', 'liquidate', 'danger', 'risk'],
    'trade-executor': ['execute', 'now', 'immediate', 'urgent', 'trade'],
    'token-researcher': ['scam', 'rug', 'fraud', 'warning', 'alert'],
    'technical-analyst': ['breakout', 'signal', 'entry', 'exit']
}

def log_delegation(agent_name, prompt, confidence, timestamp):
    """Log agent delegation decisions"""
    log_dir = os.path.expanduser('~/.claude/logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_entry = {
        'timestamp': timestamp,
        'agent': agent_name,
        'prompt_preview': prompt[:100] + '...' if len(prompt) > 100 else prompt,
        'confidence': confidence,
        'delegation_type': 'automatic'
    }
    
    with open(f"{log_dir}/agent_delegations.jsonl", 'a') as f:
        f.write(json.dumps(log_entry) + '\n')

def calculate_agent_confidence(prompt, agent_name):
    """Calculate confidence score for agent delegation"""
    prompt_lower = prompt.lower()
    patterns = AGENT_PATTERNS.get(agent_name, [])
    high_priority = HIGH_PRIORITY_KEYWORDS.get(agent_name, [])
    
    confidence = 0.0
    matches = 0
    
    # Check pattern matches
    for pattern in patterns:
        if re.search(pattern, prompt_lower):
            matches += 1
            confidence += 0.2
    
    # Boost confidence for high-priority keywords
    for keyword in high_priority:
        if keyword in prompt_lower:
            confidence += 0.3
    
    # Normalize confidence
    confidence = min(confidence, 1.0)
    
    return confidence, matches

def should_delegate_to_agent(prompt):
    """Determine if prompt should be delegated to a specific agent"""
    best_agent = None
    best_confidence = 0.0
    best_matches = 0
    
    for agent_name in AGENT_PATTERNS.keys():
        confidence, matches = calculate_agent_confidence(prompt, agent_name)
        
        if confidence > best_confidence:
            best_agent = agent_name
            best_confidence = confidence
            best_matches = matches
    
    # Only delegate if confidence is above threshold
    if best_confidence >= 0.4:
        return best_agent, best_confidence
    
    return None, 0.0

def generate_delegation_message(agent_name, original_prompt, confidence):
    """Generate delegation message for the agent"""
    delegation_messages = {
        'market-analyzer': f"Use the market-analyzer agent to: {original_prompt}",
        'risk-manager': f"Use the risk-manager agent to: {original_prompt}",
        'trade-executor': f"Use the trade-executor agent to: {original_prompt}",
        'portfolio-manager': f"Use the portfolio-manager agent to: {original_prompt}",
        'token-researcher': f"Use the token-researcher agent to: {original_prompt}",
        'technical-analyst': f"Use the technical-analyst agent to: {original_prompt}"
    }
    
    return delegation_messages.get(agent_name, original_prompt)

def check_multi_agent_workflow(prompt):
    """Check if prompt requires multiple agents in sequence"""
    prompt_lower = prompt.lower()
    
    # Common multi-agent workflows
    workflows = {
        'full_analysis': {
            'patterns': [r'complete.*analysis', r'full.*research', r'comprehensive.*review'],
            'agents': ['token-researcher', 'technical-analyst', 'risk-manager']
        },
        'trade_preparation': {
            'patterns': [r'prepare.*trade', r'ready.*to.*trade', r'analyze.*before.*trading'],
            'agents': ['market-analyzer', 'technical-analyst', 'risk-manager']
        },
        'opportunity_evaluation': {
            'patterns': [r'evaluate.*opportunity', r'assess.*token', r'should.*i.*trade'],
            'agents': ['token-researcher', 'market-analyzer', 'technical-analyst']
        }
    }
    
    for workflow_name, workflow_data in workflows.items():
        for pattern in workflow_data['patterns']:
            if re.search(pattern, prompt_lower):
                return workflow_name, workflow_data['agents']
    
    return None, []

def main():
    try:
        # Read input data
        input_data = json.load(sys.stdin)
        prompt = input_data.get('prompt', '')
        timestamp = datetime.now().isoformat()
        
        if not prompt:
            sys.exit(0)
        
        # Check for explicit agent mentions first
        explicit_agent = None
        for agent_name in AGENT_PATTERNS.keys():
            if f"use the {agent_name}" in prompt.lower() or f"{agent_name} agent" in prompt.lower():
                explicit_agent = agent_name
                break
        
        if explicit_agent:
            log_delegation(explicit_agent, prompt, 1.0, timestamp)
            print(f"✓ Explicit delegation to {explicit_agent} agent")
            sys.exit(0)
        
        # Check for multi-agent workflows
        workflow_name, workflow_agents = check_multi_agent_workflow(prompt)
        if workflow_name:
            log_entry = {
                'timestamp': timestamp,
                'workflow': workflow_name,
                'agents': workflow_agents,
                'prompt_preview': prompt[:100] + '...' if len(prompt) > 100 else prompt,
                'delegation_type': 'workflow'
            }
            
            log_dir = os.path.expanduser('~/.claude/logs')
            os.makedirs(log_dir, exist_ok=True)
            with open(f"{log_dir}/workflow_delegations.jsonl", 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
            
            print(f"✓ Multi-agent workflow detected: {workflow_name}")
            print(f"  Suggested agent sequence: {' → '.join(workflow_agents)}")
            sys.exit(0)
        
        # Check for automatic delegation
        agent_name, confidence = should_delegate_to_agent(prompt)
        
        if agent_name:
            log_delegation(agent_name, prompt, confidence, timestamp)
            delegation_msg = generate_delegation_message(agent_name, prompt, confidence)
            
            print(f"✓ Auto-delegating to {agent_name} agent (confidence: {confidence:.2f})")
            print(f"  Suggested prompt: {delegation_msg}")
        else:
            # Log non-delegated prompts for analysis
            log_entry = {
                'timestamp': timestamp,
                'prompt_preview': prompt[:100] + '...' if len(prompt) > 100 else prompt,
                'delegation_type': 'none',
                'reason': 'low_confidence'
            }
            
            log_dir = os.path.expanduser('~/.claude/logs')
            os.makedirs(log_dir, exist_ok=True)
            with open(f"{log_dir}/non_delegated_prompts.jsonl", 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
    
    except Exception as e:
        # Log errors but don't block the main workflow
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'hook': 'agent_delegator'
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        with open(f"{log_dir}/hook_errors.jsonl", 'a') as f:
            f.write(json.dumps(error_log) + '\n')
        
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
