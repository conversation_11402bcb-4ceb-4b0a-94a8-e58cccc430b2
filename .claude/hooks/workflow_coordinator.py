#!/usr/bin/env python3
"""
Workflow Coordinator Hook for Solana Memecoin Trading System
Coordinates multi-agent workflows and manages agent handoffs
"""
import json
import sys
import os
from datetime import datetime
import re

# Define trading workflows
TRADING_WORKFLOWS = {
    'token_evaluation': {
        'description': 'Complete token evaluation workflow',
        'steps': [
            {'agent': 'token-researcher', 'task': 'Research token fundamentals and detect risks'},
            {'agent': 'market-analyzer', 'task': 'Analyze market conditions and liquidity'},
            {'agent': 'technical-analyst', 'task': 'Identify optimal entry points'},
            {'agent': 'risk-manager', 'task': 'Calculate position size and risk parameters'}
        ],
        'triggers': ['evaluate token', 'analyze token', 'research token', 'token analysis']
    },
    'market_opportunity': {
        'description': 'Market opportunity identification and analysis',
        'steps': [
            {'agent': 'market-analyzer', 'task': 'Scan for market opportunities'},
            {'agent': 'technical-analyst', 'task': 'Confirm technical signals'},
            {'agent': 'token-researcher', 'task': 'Quick due diligence on opportunities'},
            {'agent': 'risk-manager', 'task': 'Assess risk-reward ratio'}
        ],
        'triggers': ['find opportunities', 'market scan', 'discover trades']
    },
    'trade_execution': {
        'description': 'Complete trade execution workflow',
        'steps': [
            {'agent': 'risk-manager', 'task': 'Final risk check and position sizing'},
            {'agent': 'trade-executor', 'task': 'Execute the trade'},
            {'agent': 'portfolio-manager', 'task': 'Update portfolio tracking'}
        ],
        'triggers': ['execute trade', 'make trade', 'buy token', 'sell token']
    },
    'portfolio_review': {
        'description': 'Comprehensive portfolio review',
        'steps': [
            {'agent': 'portfolio-manager', 'task': 'Generate portfolio performance report'},
            {'agent': 'risk-manager', 'task': 'Assess current portfolio risks'},
            {'agent': 'market-analyzer', 'task': 'Analyze market impact on holdings'},
            {'agent': 'technical-analyst', 'task': 'Review exit signals for positions'}
        ],
        'triggers': ['portfolio review', 'check portfolio', 'portfolio analysis']
    },
    'emergency_response': {
        'description': 'Emergency market response workflow',
        'steps': [
            {'agent': 'market-analyzer', 'task': 'Assess market crisis severity'},
            {'agent': 'risk-manager', 'task': 'Activate emergency protocols'},
            {'agent': 'portfolio-manager', 'task': 'Calculate potential losses'},
            {'agent': 'trade-executor', 'task': 'Execute emergency trades if needed'}
        ],
        'triggers': ['emergency', 'market crash', 'stop loss', 'liquidate']
    }
}

def log_workflow_event(event_type, workflow_name, step_info, timestamp):
    """Log workflow coordination events"""
    log_dir = os.path.expanduser('~/.claude/logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_entry = {
        'timestamp': timestamp,
        'event_type': event_type,
        'workflow': workflow_name,
        'step_info': step_info
    }
    
    with open(f"{log_dir}/workflow_coordination.jsonl", 'a') as f:
        f.write(json.dumps(log_entry) + '\n')

def detect_workflow_trigger(context_data):
    """Detect if retrieved context suggests a specific workflow"""
    context_text = str(context_data).lower()
    
    for workflow_name, workflow_data in TRADING_WORKFLOWS.items():
        for trigger in workflow_data['triggers']:
            if trigger in context_text:
                return workflow_name, workflow_data
    
    return None, None

def get_workflow_state():
    """Get current workflow state from file"""
    state_file = os.path.expanduser('~/.claude/workflow_state.json')
    
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r') as f:
                return json.load(f)
        except:
            pass
    
    return {'active_workflow': None, 'current_step': 0, 'completed_steps': []}

def save_workflow_state(state):
    """Save workflow state to file"""
    state_file = os.path.expanduser('~/.claude/workflow_state.json')
    
    with open(state_file, 'w') as f:
        json.dump(state, f, indent=2)

def advance_workflow(workflow_name, workflow_data, current_state):
    """Advance to next step in workflow"""
    current_step = current_state.get('current_step', 0)
    steps = workflow_data['steps']
    
    if current_step < len(steps):
        next_step = steps[current_step]
        
        # Update state
        new_state = {
            'active_workflow': workflow_name,
            'current_step': current_step + 1,
            'completed_steps': current_state.get('completed_steps', []) + [current_step],
            'workflow_start_time': current_state.get('workflow_start_time', datetime.now().isoformat())
        }
        
        save_workflow_state(new_state)
        
        return next_step
    
    return None

def complete_workflow(workflow_name):
    """Mark workflow as completed"""
    state = get_workflow_state()
    
    completion_state = {
        'active_workflow': None,
        'current_step': 0,
        'completed_steps': [],
        'last_completed_workflow': workflow_name,
        'completion_time': datetime.now().isoformat()
    }
    
    save_workflow_state(completion_state)

def generate_next_step_prompt(step_info, workflow_context):
    """Generate prompt for next workflow step"""
    agent = step_info['agent']
    task = step_info['task']
    
    prompt_templates = {
        'token-researcher': f"Use the token-researcher agent to: {task}. Context: {workflow_context}",
        'market-analyzer': f"Use the market-analyzer agent to: {task}. Context: {workflow_context}",
        'technical-analyst': f"Use the technical-analyst agent to: {task}. Context: {workflow_context}",
        'risk-manager': f"Use the risk-manager agent to: {task}. Context: {workflow_context}",
        'trade-executor': f"Use the trade-executor agent to: {task}. Context: {workflow_context}",
        'portfolio-manager': f"Use the portfolio-manager agent to: {task}. Context: {workflow_context}"
    }
    
    return prompt_templates.get(agent, f"Use the {agent} agent to: {task}")

def main():
    try:
        # Read input data
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_input = input_data.get('tool_input', {})
        tool_output = input_data.get('tool_output', '')
        timestamp = datetime.now().isoformat()
        
        # Only process codebase-retrieval tool calls
        if tool_name != 'codebase-retrieval':
            sys.exit(0)
        
        # Get current workflow state
        current_state = get_workflow_state()
        
        # Check if we're in an active workflow
        if current_state.get('active_workflow'):
            workflow_name = current_state['active_workflow']
            workflow_data = TRADING_WORKFLOWS.get(workflow_name)
            
            if workflow_data:
                next_step = advance_workflow(workflow_name, workflow_data, current_state)
                
                if next_step:
                    log_workflow_event('step_advance', workflow_name, next_step, timestamp)
                    
                    prompt = generate_next_step_prompt(next_step, tool_input.get('information_request', ''))
                    
                    print(f"✓ Workflow step completed: {workflow_name}")
                    print(f"  Next step: {next_step['agent']} - {next_step['task']}")
                    print(f"  Suggested prompt: {prompt}")
                else:
                    # Workflow completed
                    complete_workflow(workflow_name)
                    log_workflow_event('workflow_complete', workflow_name, {}, timestamp)
                    
                    print(f"✓ Workflow completed: {workflow_name}")
                    print("  All steps have been executed successfully")
        else:
            # Check if context suggests starting a new workflow
            workflow_name, workflow_data = detect_workflow_trigger(tool_input)
            
            if workflow_name and workflow_data:
                # Start new workflow
                new_state = {
                    'active_workflow': workflow_name,
                    'current_step': 0,
                    'completed_steps': [],
                    'workflow_start_time': timestamp
                }
                
                save_workflow_state(new_state)
                
                first_step = workflow_data['steps'][0]
                
                log_workflow_event('workflow_start', workflow_name, first_step, timestamp)
                
                prompt = generate_next_step_prompt(first_step, tool_input.get('information_request', ''))
                
                print(f"✓ Starting workflow: {workflow_name}")
                print(f"  Description: {workflow_data['description']}")
                print(f"  First step: {first_step['agent']} - {first_step['task']}")
                print(f"  Suggested prompt: {prompt}")
    
    except Exception as e:
        # Log errors but don't block the main workflow
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'hook': 'workflow_coordinator'
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        with open(f"{log_dir}/hook_errors.jsonl", 'a') as f:
            f.write(json.dumps(error_log) + '\n')
        
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
