#!/usr/bin/env python3
"""
Trade Execution Guard Hook for Solana Memecoin Trading System
Provides safety checks and validation before executing trades
"""
import json
import sys
import os
import re
from datetime import datetime

# Dangerous command patterns that should be blocked or require confirmation
DANGEROUS_PATTERNS = [
    r'curl.*swap.*jupiter',
    r'solana.*transfer',
    r'spl-token.*transfer',
    r'phantom.*send',
    r'wallet.*send',
    r'private.*key',
    r'mnemonic.*phrase',
    r'seed.*phrase'
]

# Trading-related patterns that need validation
TRADING_PATTERNS = [
    r'swap.*token',
    r'buy.*\$[A-Z]+',
    r'sell.*\$[A-Z]+',
    r'jupiter.*api',
    r'raydium.*swap',
    r'orca.*trade'
]

# Maximum trade amounts (in USD equivalent)
MAX_TRADE_AMOUNTS = {
    'single_trade': 10000,  # $10k max per trade
    'daily_total': 50000,   # $50k max per day
    'portfolio_percentage': 0.1  # 10% max of portfolio per trade
}

def log_execution_event(event_type, command, risk_level, timestamp, details=None):
    """Log trade execution events"""
    log_dir = os.path.expanduser('~/.claude/logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_entry = {
        'timestamp': timestamp,
        'event_type': event_type,
        'command_preview': command[:100] + '...' if len(command) > 100 else command,
        'risk_level': risk_level,
        'details': details or {}
    }
    
    with open(f"{log_dir}/trade_execution_guard.jsonl", 'a') as f:
        f.write(json.dumps(log_entry) + '\n')

def check_dangerous_patterns(command):
    """Check for dangerous command patterns"""
    command_lower = command.lower()
    
    for pattern in DANGEROUS_PATTERNS:
        if re.search(pattern, command_lower):
            return True, pattern
    
    return False, None

def check_trading_patterns(command):
    """Check for trading-related patterns"""
    command_lower = command.lower()
    
    for pattern in TRADING_PATTERNS:
        if re.search(pattern, command_lower):
            return True, pattern
    
    return False, None

def extract_trade_amount(command):
    """Extract trade amount from command if present"""
    # Look for amount patterns like "1000 USDC", "$1000", "1000 SOL"
    amount_patterns = [
        r'\$(\d+(?:\.\d+)?)',
        r'(\d+(?:\.\d+)?)\s*USDC',
        r'(\d+(?:\.\d+)?)\s*SOL',
        r'amount[:\s]*(\d+(?:\.\d+)?)',
        r'value[:\s]*(\d+(?:\.\d+)?)'
    ]
    
    for pattern in amount_patterns:
        match = re.search(pattern, command, re.IGNORECASE)
        if match:
            return float(match.group(1))
    
    return None

def get_daily_trade_volume():
    """Get today's total trade volume from logs"""
    log_dir = os.path.expanduser('~/.claude/logs')
    log_file = f"{log_dir}/trade_execution_guard.jsonl"
    
    if not os.path.exists(log_file):
        return 0.0
    
    today = datetime.now().strftime('%Y-%m-%d')
    daily_volume = 0.0
    
    try:
        with open(log_file, 'r') as f:
            for line in f:
                entry = json.loads(line.strip())
                if entry['timestamp'].startswith(today) and entry['event_type'] == 'trade_approved':
                    amount = entry.get('details', {}).get('trade_amount', 0)
                    if amount:
                        daily_volume += amount
    except:
        pass
    
    return daily_volume

def validate_trade_limits(trade_amount):
    """Validate trade against configured limits"""
    if not trade_amount:
        return True, "No amount detected"
    
    # Check single trade limit
    if trade_amount > MAX_TRADE_AMOUNTS['single_trade']:
        return False, f"Trade amount ${trade_amount:,.2f} exceeds single trade limit of ${MAX_TRADE_AMOUNTS['single_trade']:,.2f}"
    
    # Check daily limit
    daily_volume = get_daily_trade_volume()
    if daily_volume + trade_amount > MAX_TRADE_AMOUNTS['daily_total']:
        return False, f"Trade would exceed daily limit. Current: ${daily_volume:,.2f}, Limit: ${MAX_TRADE_AMOUNTS['daily_total']:,.2f}"
    
    return True, "Trade within limits"

def check_wallet_security(command):
    """Check for wallet security issues"""
    security_issues = []
    
    # Check for private key exposure
    if re.search(r'private.*key|mnemonic|seed.*phrase', command, re.IGNORECASE):
        security_issues.append("Potential private key or seed phrase exposure")
    
    # Check for hardcoded addresses
    if re.search(r'[1-9A-HJ-NP-Za-km-z]{32,44}', command):
        security_issues.append("Hardcoded wallet address detected")
    
    # Check for insecure API usage
    if re.search(r'http://.*api', command, re.IGNORECASE):
        security_issues.append("Insecure HTTP API call detected")
    
    return security_issues

def generate_safety_report(command, risk_assessment):
    """Generate safety report for the command"""
    report = {
        'command_type': 'unknown',
        'risk_level': 'low',
        'safety_checks': [],
        'recommendations': []
    }
    
    # Determine command type
    if risk_assessment['is_dangerous']:
        report['command_type'] = 'dangerous'
        report['risk_level'] = 'high'
    elif risk_assessment['is_trading']:
        report['command_type'] = 'trading'
        report['risk_level'] = 'medium'
    
    # Add safety checks
    if risk_assessment['trade_amount']:
        report['safety_checks'].append(f"Trade amount: ${risk_assessment['trade_amount']:,.2f}")
    
    if risk_assessment['daily_volume']:
        report['safety_checks'].append(f"Daily volume: ${risk_assessment['daily_volume']:,.2f}")
    
    if risk_assessment['security_issues']:
        report['safety_checks'].extend(risk_assessment['security_issues'])
        report['risk_level'] = 'high'
    
    # Add recommendations
    if report['risk_level'] == 'high':
        report['recommendations'].append("Manual review required before execution")
        report['recommendations'].append("Verify all parameters carefully")
    elif report['risk_level'] == 'medium':
        report['recommendations'].append("Confirm trade parameters")
        report['recommendations'].append("Check market conditions")
    
    return report

def main():
    try:
        # Read input data
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_input = input_data.get('tool_input', {})
        command = tool_input.get('command', '')
        timestamp = datetime.now().isoformat()
        
        # Only process launch-process tool calls
        if tool_name != 'launch-process' or not command:
            sys.exit(0)
        
        # Perform risk assessment
        is_dangerous, dangerous_pattern = check_dangerous_patterns(command)
        is_trading, trading_pattern = check_trading_patterns(command)
        trade_amount = extract_trade_amount(command)
        security_issues = check_wallet_security(command)
        daily_volume = get_daily_trade_volume()
        
        risk_assessment = {
            'is_dangerous': is_dangerous,
            'dangerous_pattern': dangerous_pattern,
            'is_trading': is_trading,
            'trading_pattern': trading_pattern,
            'trade_amount': trade_amount,
            'security_issues': security_issues,
            'daily_volume': daily_volume
        }
        
        # Generate safety report
        safety_report = generate_safety_report(command, risk_assessment)
        
        # Determine if command should be blocked
        should_block = False
        block_reason = None
        
        if is_dangerous:
            should_block = True
            block_reason = f"Dangerous pattern detected: {dangerous_pattern}"
        
        if security_issues:
            should_block = True
            block_reason = f"Security issues: {', '.join(security_issues)}"
        
        if trade_amount:
            is_valid, limit_message = validate_trade_limits(trade_amount)
            if not is_valid:
                should_block = True
                block_reason = limit_message
        
        # Log the event
        event_type = 'trade_blocked' if should_block else ('trade_approved' if is_trading else 'command_approved')
        
        log_execution_event(
            event_type, 
            command, 
            safety_report['risk_level'], 
            timestamp,
            {
                'trade_amount': trade_amount,
                'daily_volume': daily_volume,
                'security_issues': security_issues,
                'block_reason': block_reason
            }
        )
        
        # Output results
        if should_block:
            print(f"🚫 TRADE EXECUTION BLOCKED")
            print(f"   Reason: {block_reason}")
            print(f"   Risk Level: {safety_report['risk_level'].upper()}")
            if safety_report['recommendations']:
                print(f"   Recommendations: {', '.join(safety_report['recommendations'])}")
            sys.exit(2)  # Block the command
        
        elif is_trading or safety_report['risk_level'] != 'low':
            print(f"⚠️  Trade Execution Safety Check")
            print(f"   Command Type: {safety_report['command_type']}")
            print(f"   Risk Level: {safety_report['risk_level'].upper()}")
            
            if trade_amount:
                print(f"   Trade Amount: ${trade_amount:,.2f}")
                print(f"   Daily Volume: ${daily_volume:,.2f}")
            
            if safety_report['recommendations']:
                print(f"   Recommendations: {', '.join(safety_report['recommendations'])}")
            
            print(f"   ✓ Command approved for execution")
    
    except Exception as e:
        # Log errors but don't block the main workflow
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'hook': 'trade_execution_guard'
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        with open(f"{log_dir}/hook_errors.jsonl", 'a') as f:
            f.write(json.dumps(error_log) + '\n')
        
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
