#!/usr/bin/env python3
"""
Session Logger Hook for Solana Memecoin Trading System
Logs session activities and agent interactions
"""
import json
import sys
import os
from datetime import datetime

def main():
    try:
        input_data = json.load(sys.stdin)
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'event': 'session_stop',
            'data': input_data
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        
        with open(f"{log_dir}/session_activities.jsonl", 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
    
    except Exception as e:
        pass  # Silent logging

if __name__ == "__main__":
    main()
