#!/usr/bin/env python3
"""
Market Data Validator Hook for Solana Memecoin Trading System
Validates market data requests and ensures data quality
"""
import json
import sys
import os
import re
from datetime import datetime

# Trusted data sources
TRUSTED_SOURCES = [
    'dexscreener.com',
    'birdeye.so',
    'coingecko.com',
    'solscan.io',
    'jupiter.ag',
    'raydium.io',
    'orca.so'
]

# Suspicious patterns that might indicate unreliable sources
SUSPICIOUS_PATTERNS = [
    r'pump\.fun.*fake',
    r'scam.*token',
    r'rug.*pull',
    r'fake.*volume',
    r'bot.*trading'
]

# Rate limiting configuration
RATE_LIMITS = {
    'requests_per_minute': 60,
    'requests_per_hour': 1000,
    'max_concurrent': 5
}

def log_data_request(source, request_type, validation_result, timestamp):
    """Log market data requests for monitoring"""
    log_dir = os.path.expanduser('~/.claude/logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_entry = {
        'timestamp': timestamp,
        'source': source,
        'request_type': request_type,
        'validation_result': validation_result
    }
    
    with open(f"{log_dir}/market_data_requests.jsonl", 'a') as f:
        f.write(json.dumps(log_entry) + '\n')

def extract_source_from_url(url):
    """Extract domain from URL"""
    import urllib.parse
    
    try:
        parsed = urllib.parse.urlparse(url)
        return parsed.netloc.lower()
    except:
        return url.lower()

def validate_data_source(url_or_query):
    """Validate if data source is trusted"""
    source = extract_source_from_url(url_or_query)
    
    # Check if source is in trusted list
    for trusted in TRUSTED_SOURCES:
        if trusted in source:
            return True, f"Trusted source: {trusted}"
    
    # Check for suspicious patterns
    for pattern in SUSPICIOUS_PATTERNS:
        if re.search(pattern, url_or_query.lower()):
            return False, f"Suspicious pattern detected: {pattern}"
    
    # Unknown source - proceed with caution
    return True, f"Unknown source: {source} - proceed with caution"

def check_rate_limits():
    """Check if we're within rate limits"""
    log_dir = os.path.expanduser('~/.claude/logs')
    log_file = f"{log_dir}/market_data_requests.jsonl"
    
    if not os.path.exists(log_file):
        return True, "No previous requests"
    
    now = datetime.now()
    minute_ago = now.timestamp() - 60
    hour_ago = now.timestamp() - 3600
    
    minute_count = 0
    hour_count = 0
    
    try:
        with open(log_file, 'r') as f:
            for line in f:
                entry = json.loads(line.strip())
                entry_time = datetime.fromisoformat(entry['timestamp']).timestamp()
                
                if entry_time > minute_ago:
                    minute_count += 1
                if entry_time > hour_ago:
                    hour_count += 1
    except:
        pass
    
    if minute_count >= RATE_LIMITS['requests_per_minute']:
        return False, f"Rate limit exceeded: {minute_count} requests in last minute"
    
    if hour_count >= RATE_LIMITS['requests_per_hour']:
        return False, f"Rate limit exceeded: {hour_count} requests in last hour"
    
    return True, f"Within limits: {minute_count}/min, {hour_count}/hour"

def validate_token_address(address):
    """Validate Solana token address format"""
    if not address:
        return True, "No address provided"
    
    # Solana addresses are base58 encoded and typically 32-44 characters
    if len(address) < 32 or len(address) > 44:
        return False, f"Invalid address length: {len(address)}"
    
    # Check for valid base58 characters
    valid_chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
    if not all(c in valid_chars for c in address):
        return False, "Invalid characters in address"
    
    return True, "Valid address format"

def extract_token_info(query_or_url):
    """Extract token information from query or URL"""
    token_info = {
        'symbol': None,
        'address': None,
        'pair': None
    }
    
    # Extract token symbol (e.g., $BONK, $SOL)
    symbol_match = re.search(r'\$([A-Z]{2,10})', query_or_url.upper())
    if symbol_match:
        token_info['symbol'] = symbol_match.group(1)
    
    # Extract potential token address
    address_match = re.search(r'[1-9A-HJ-NP-Za-km-z]{32,44}', query_or_url)
    if address_match:
        token_info['address'] = address_match.group(0)
    
    # Extract trading pair
    pair_match = re.search(r'([A-Z]+)[/-]([A-Z]+)', query_or_url.upper())
    if pair_match:
        token_info['pair'] = f"{pair_match.group(1)}/{pair_match.group(2)}"
    
    return token_info

def main():
    try:
        # Read input data
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_input = input_data.get('tool_input', {})
        timestamp = datetime.now().isoformat()
        
        # Only process web-search and web-fetch tools
        if tool_name not in ['web-search', 'web-fetch']:
            sys.exit(0)
        
        # Extract request details
        if tool_name == 'web-search':
            query = tool_input.get('query', '')
            url_or_query = query
            request_type = 'search'
        else:  # web-fetch
            url = tool_input.get('url', '')
            url_or_query = url
            request_type = 'fetch'
        
        if not url_or_query:
            sys.exit(0)
        
        # Validate data source
        is_trusted, source_message = validate_data_source(url_or_query)
        
        # Check rate limits
        within_limits, limit_message = check_rate_limits()
        
        # Extract token information
        token_info = extract_token_info(url_or_query)
        
        # Validate token address if present
        address_valid = True
        address_message = "No address to validate"
        if token_info['address']:
            address_valid, address_message = validate_token_address(token_info['address'])
        
        # Compile validation results
        validation_result = {
            'trusted_source': is_trusted,
            'source_message': source_message,
            'within_rate_limits': within_limits,
            'limit_message': limit_message,
            'address_valid': address_valid,
            'address_message': address_message,
            'token_info': token_info
        }
        
        # Log the request
        source = extract_source_from_url(url_or_query)
        log_data_request(source, request_type, validation_result, timestamp)
        
        # Determine if request should be blocked
        should_block = False
        block_reason = None
        
        if not is_trusted and 'suspicious pattern' in source_message.lower():
            should_block = True
            block_reason = source_message
        
        if not within_limits:
            should_block = True
            block_reason = limit_message
        
        if not address_valid:
            should_block = True
            block_reason = address_message
        
        # Output validation results
        if should_block:
            print(f"🚫 MARKET DATA REQUEST BLOCKED")
            print(f"   Reason: {block_reason}")
            print(f"   Source: {source}")
            sys.exit(2)  # Block the request
        
        else:
            print(f"✓ Market Data Request Validated")
            print(f"  Source: {source} ({source_message})")
            print(f"  Rate Limits: {limit_message}")
            
            if token_info['symbol']:
                print(f"  Token Symbol: ${token_info['symbol']}")
            
            if token_info['address']:
                print(f"  Token Address: {token_info['address'][:8]}...{token_info['address'][-8:]}")
            
            if token_info['pair']:
                print(f"  Trading Pair: {token_info['pair']}")
            
            if not is_trusted:
                print(f"  ⚠️  Warning: {source_message}")
    
    except Exception as e:
        # Log errors but don't block the main workflow
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'hook': 'market_data_validator'
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        with open(f"{log_dir}/hook_errors.jsonl", 'a') as f:
            f.write(json.dumps(error_log) + '\n')
        
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
