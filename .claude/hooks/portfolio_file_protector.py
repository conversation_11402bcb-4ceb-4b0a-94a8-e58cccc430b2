#!/usr/bin/env python3
"""
Portfolio File Protector Hook for Solana Memecoin Trading System
Protects critical portfolio and configuration files from accidental modification
"""
import json
import sys
import os
from datetime import datetime

# Protected file patterns
PROTECTED_PATTERNS = [
    r'\.env',
    r'private.*key',
    r'wallet.*json',
    r'seed.*phrase',
    r'mnemonic',
    r'portfolio.*state',
    r'trading.*config'
]

def main():
    try:
        input_data = json.load(sys.stdin)
        tool_input = input_data.get('tool_input', {})
        file_path = tool_input.get('file_path', '') or tool_input.get('path', '')
        
        if not file_path:
            sys.exit(0)
        
        # Check if file is protected
        for pattern in PROTECTED_PATTERNS:
            if pattern in file_path.lower():
                print(f"🚫 PROTECTED FILE ACCESS BLOCKED")
                print(f"   File: {file_path}")
                print(f"   Pattern: {pattern}")
                sys.exit(2)  # Block the operation
        
        print(f"✓ File access approved: {file_path}")
    
    except Exception as e:
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
