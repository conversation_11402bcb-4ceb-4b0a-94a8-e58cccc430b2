#!/usr/bin/env python3
"""
Subagent Result Processor Hook for Solana Memecoin Trading System
Processes and coordinates results from specialized sub-agents
"""
import json
import sys
import os
import re
from datetime import datetime

# Agent result patterns and expected outputs
AGENT_RESULT_PATTERNS = {
    'market-analyzer': {
        'success_patterns': [
            r'market\s+analysis\s+complete',
            r'opportunities?\s+identified',
            r'trend\s+analysis',
            r'volume\s+analysis'
        ],
        'data_extraction': {
            'opportunities': r'opportunity[:\s]*([^\n]+)',
            'trend': r'trend[:\s]*([^\n]+)',
            'volume': r'volume[:\s]*([^\n]+)',
            'risk_level': r'risk[:\s]*([^\n]+)'
        }
    },
    'risk-manager': {
        'success_patterns': [
            r'position\s+size\s+calculated',
            r'risk\s+assessment\s+complete',
            r'stop\s+loss\s+set',
            r'risk\s+parameters'
        ],
        'data_extraction': {
            'position_size': r'position\s+size[:\s]*([^\n]+)',
            'stop_loss': r'stop\s+loss[:\s]*([^\n]+)',
            'risk_score': r'risk\s+score[:\s]*([^\n]+)',
            'max_loss': r'max\s+loss[:\s]*([^\n]+)'
        }
    },
    'trade-executor': {
        'success_patterns': [
            r'trade\s+executed',
            r'transaction\s+confirmed',
            r'swap\s+completed',
            r'order\s+filled'
        ],
        'data_extraction': {
            'transaction_hash': r'hash[:\s]*([a-zA-Z0-9]+)',
            'execution_price': r'price[:\s]*([^\n]+)',
            'slippage': r'slippage[:\s]*([^\n]+)',
            'gas_fee': r'fee[:\s]*([^\n]+)'
        }
    },
    'portfolio-manager': {
        'success_patterns': [
            r'portfolio\s+updated',
            r'performance\s+calculated',
            r'positions\s+tracked',
            r'pnl\s+calculated'
        ],
        'data_extraction': {
            'total_value': r'total\s+value[:\s]*([^\n]+)',
            'pnl': r'pnl[:\s]*([^\n]+)',
            'return': r'return[:\s]*([^\n]+)',
            'positions': r'positions[:\s]*([^\n]+)'
        }
    },
    'token-researcher': {
        'success_patterns': [
            r'research\s+complete',
            r'due\s+diligence\s+finished',
            r'token\s+analyzed',
            r'risk\s+assessment'
        ],
        'data_extraction': {
            'risk_rating': r'risk\s+rating[:\s]*([^\n]+)',
            'recommendation': r'recommendation[:\s]*([^\n]+)',
            'red_flags': r'red\s+flags?[:\s]*([^\n]+)',
            'score': r'score[:\s]*([^\n]+)'
        }
    },
    'technical-analyst': {
        'success_patterns': [
            r'technical\s+analysis\s+complete',
            r'signals?\s+identified',
            r'chart\s+analysis',
            r'entry\s+point'
        ],
        'data_extraction': {
            'signal': r'signal[:\s]*([^\n]+)',
            'entry_price': r'entry[:\s]*([^\n]+)',
            'target': r'target[:\s]*([^\n]+)',
            'confidence': r'confidence[:\s]*([^\n]+)'
        }
    }
}

def log_subagent_result(agent_name, result_data, extracted_data, timestamp):
    """Log subagent results for coordination"""
    log_dir = os.path.expanduser('~/.claude/logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_entry = {
        'timestamp': timestamp,
        'agent': agent_name,
        'result_summary': result_data[:200] + '...' if len(result_data) > 200 else result_data,
        'extracted_data': extracted_data,
        'success': bool(extracted_data)
    }
    
    with open(f"{log_dir}/subagent_results.jsonl", 'a') as f:
        f.write(json.dumps(log_entry) + '\n')

def identify_agent_from_result(result_text):
    """Identify which agent produced the result"""
    result_lower = result_text.lower()
    
    # Check for explicit agent mentions
    for agent_name in AGENT_RESULT_PATTERNS.keys():
        if agent_name.replace('-', ' ') in result_lower or agent_name in result_lower:
            return agent_name
    
    # Check for agent-specific patterns
    for agent_name, patterns in AGENT_RESULT_PATTERNS.items():
        for pattern in patterns['success_patterns']:
            if re.search(pattern, result_lower):
                return agent_name
    
    return None

def extract_agent_data(agent_name, result_text):
    """Extract structured data from agent results"""
    if agent_name not in AGENT_RESULT_PATTERNS:
        return {}
    
    extraction_patterns = AGENT_RESULT_PATTERNS[agent_name]['data_extraction']
    extracted_data = {}
    
    for key, pattern in extraction_patterns.items():
        match = re.search(pattern, result_text, re.IGNORECASE)
        if match:
            extracted_data[key] = match.group(1).strip()
    
    return extracted_data

def determine_next_action(agent_name, extracted_data):
    """Determine what action should be taken next based on agent results"""
    next_actions = {
        'market-analyzer': {
            'condition': lambda data: 'opportunities' in data or 'trend' in data,
            'action': 'Use token-researcher to analyze identified opportunities',
            'priority': 'medium'
        },
        'token-researcher': {
            'condition': lambda data: data.get('recommendation', '').lower() in ['buy', 'strong buy', 'positive'],
            'action': 'Use technical-analyst to find optimal entry points',
            'priority': 'high'
        },
        'technical-analyst': {
            'condition': lambda data: 'entry_price' in data or 'signal' in data,
            'action': 'Use risk-manager to calculate position size',
            'priority': 'high'
        },
        'risk-manager': {
            'condition': lambda data: 'position_size' in data and 'stop_loss' in data,
            'action': 'Use trade-executor to execute the trade',
            'priority': 'high'
        },
        'trade-executor': {
            'condition': lambda data: 'transaction_hash' in data,
            'action': 'Use portfolio-manager to update portfolio tracking',
            'priority': 'medium'
        },
        'portfolio-manager': {
            'condition': lambda data: 'total_value' in data or 'pnl' in data,
            'action': 'Portfolio update complete - monitor positions',
            'priority': 'low'
        }
    }
    
    if agent_name in next_actions:
        action_config = next_actions[agent_name]
        if action_config['condition'](extracted_data):
            return action_config['action'], action_config['priority']
    
    return None, 'low'

def check_workflow_completion(agent_name, extracted_data):
    """Check if current workflow step is complete"""
    # Load workflow state
    state_file = os.path.expanduser('~/.claude/workflow_state.json')
    
    if not os.path.exists(state_file):
        return False
    
    try:
        with open(state_file, 'r') as f:
            workflow_state = json.load(f)
        
        active_workflow = workflow_state.get('active_workflow')
        if not active_workflow:
            return False
        
        # Check if this agent result completes the current workflow step
        current_step = workflow_state.get('current_step', 0)
        
        # Simple completion check based on extracted data
        completion_indicators = {
            'market-analyzer': ['opportunities', 'trend'],
            'token-researcher': ['recommendation', 'risk_rating'],
            'technical-analyst': ['signal', 'entry_price'],
            'risk-manager': ['position_size', 'stop_loss'],
            'trade-executor': ['transaction_hash'],
            'portfolio-manager': ['total_value', 'pnl']
        }
        
        if agent_name in completion_indicators:
            required_fields = completion_indicators[agent_name]
            return any(field in extracted_data for field in required_fields)
        
    except:
        pass
    
    return False

def generate_coordination_summary(agent_name, extracted_data, next_action):
    """Generate summary for main agent coordination"""
    summary = {
        'agent': agent_name,
        'status': 'completed',
        'key_results': extracted_data,
        'next_action': next_action,
        'timestamp': datetime.now().isoformat()
    }
    
    return summary

def main():
    try:
        # Read input data
        input_data = json.load(sys.stdin)
        subagent_name = input_data.get('subagent_name', '')
        subagent_result = input_data.get('subagent_result', '')
        timestamp = datetime.now().isoformat()
        
        if not subagent_result:
            sys.exit(0)
        
        # Identify the agent if not explicitly provided
        if not subagent_name:
            subagent_name = identify_agent_from_result(subagent_result)
        
        if not subagent_name:
            # Log unidentified results
            log_entry = {
                'timestamp': timestamp,
                'event': 'unidentified_subagent_result',
                'result_preview': subagent_result[:100] + '...' if len(subagent_result) > 100 else subagent_result
            }
            
            log_dir = os.path.expanduser('~/.claude/logs')
            os.makedirs(log_dir, exist_ok=True)
            with open(f"{log_dir}/unidentified_results.jsonl", 'a') as f:
                f.write(json.dumps(log_entry) + '\n')
            
            sys.exit(0)
        
        # Extract structured data from the result
        extracted_data = extract_agent_data(subagent_name, subagent_result)
        
        # Log the result
        log_subagent_result(subagent_name, subagent_result, extracted_data, timestamp)
        
        # Determine next action
        next_action, priority = determine_next_action(subagent_name, extracted_data)
        
        # Check workflow completion
        workflow_complete = check_workflow_completion(subagent_name, extracted_data)
        
        # Generate coordination summary
        coordination_summary = generate_coordination_summary(subagent_name, extracted_data, next_action)
        
        # Save coordination data for main agent
        coord_dir = os.path.expanduser('~/.claude/coordination')
        os.makedirs(coord_dir, exist_ok=True)
        
        with open(f"{coord_dir}/latest_result.json", 'w') as f:
            json.dump(coordination_summary, f, indent=2)
        
        # Output coordination information
        print(f"✓ Subagent result processed: {subagent_name}")
        
        if extracted_data:
            print(f"  Key data extracted: {list(extracted_data.keys())}")
        
        if next_action:
            print(f"  Recommended next action: {next_action}")
            print(f"  Priority: {priority.upper()}")
        
        if workflow_complete:
            print(f"  ✓ Workflow step completed")
    
    except Exception as e:
        # Log errors but don't block the main workflow
        error_log = {
            'timestamp': datetime.now().isoformat(),
            'error': str(e),
            'hook': 'subagent_result_processor'
        }
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        with open(f"{log_dir}/hook_errors.jsonl", 'a') as f:
            f.write(json.dumps(error_log) + '\n')
        
        sys.exit(0)  # Don't block on errors

if __name__ == "__main__":
    main()
