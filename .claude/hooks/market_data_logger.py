#!/usr/bin/env python3
"""
Market Data Logger Hook for Solana Memecoin Trading System
Logs market data for analysis and historical tracking
"""
import json
import sys
import os
from datetime import datetime

def main():
    try:
        input_data = json.load(sys.stdin)
        tool_name = input_data.get('tool_name', '')
        tool_output = input_data.get('tool_output', '')
        timestamp = datetime.now().isoformat()
        
        if tool_name not in ['web-search', 'web-fetch'] or not tool_output:
            sys.exit(0)
        
        log_dir = os.path.expanduser('~/.claude/logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_entry = {
            'timestamp': timestamp,
            'tool': tool_name,
            'data_preview': tool_output[:500] + '...' if len(tool_output) > 500 else tool_output
        }
        
        with open(f"{log_dir}/market_data_log.jsonl", 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
        
        print(f"✓ Market data logged: {len(tool_output)} characters")
    
    except Exception as e:
        pass  # Silent logging

if __name__ == "__main__":
    main()
